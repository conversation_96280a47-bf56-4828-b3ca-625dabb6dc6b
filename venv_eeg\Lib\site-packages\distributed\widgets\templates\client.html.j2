<div>
    <div style="width: 24px; height: 24px; background-color: #e1e1e1; border: 3px solid #9D9D9D; border-radius: 5px; position: absolute;"> </div>
    <div style="margin-left: 48px;">
        <h3 style="margin-bottom: 0px;">Client</h3>
        <p style="color: #9D9D9D; margin-bottom: 0px;">{{ id }}</p>
        <table style="width: 100%; text-align: left;">

        <tr>
        {% if not cluster and not scheduler_file %}
            <td style="text-align: left;"><strong>Connection method:</strong> Direct</td>
            <td style="text-align: left;"></td>
        {% elif cluster %}
            <td style="text-align: left;"><strong>Connection method:</strong> Cluster object</td>
            <td style="text-align: left;"><strong>Cluster type:</strong> {{ cluster | type | typename(short=True) }}</td>
        {% elif scheduler_file %}
            <td style="text-align: left;"><strong>Connection method:</strong> Scheduler file</td>
            <td style="text-align: left;"><strong>Scheduler file:</strong> {{ scheduler_file }}</td>
        {% endif %}
        </tr>

        {% if dashboard_link %}
            <tr>
                <td style="text-align: left;">
                    <strong>Dashboard: </strong> <a href="{{ dashboard_link }}" target="_blank">{{ dashboard_link }}</a>
                </td>
                <td style="text-align: left;"></td>
            </tr>
        {% endif %}

        </table>

        {% if jupyterlab %}
            <button style="margin-bottom: 12px;" data-commandlinker-command="dask:populate-and-launch-layout" data-commandlinker-args='{"url": "{{ dashboard_link }}" }'>
                Launch dashboard in JupyterLab
            </button>
        {% endif %}

        {% if scheduler is none %}
            <p>No scheduler connected.</p>
        {% elif cluster %}
            <details>
            <summary style="margin-bottom: 20px;"><h3 style="display: inline;">Cluster Info</h3></summary>
            {{ cluster._repr_html_() }}
            </details>
        {% else %}
            <details>
            <summary style="margin-bottom: 20px;"><h3 style="display: inline;">Scheduler Info</h3></summary>
            {{ info._repr_html_() }}
            </details>
        {% endif %}

    </div>
</div>
