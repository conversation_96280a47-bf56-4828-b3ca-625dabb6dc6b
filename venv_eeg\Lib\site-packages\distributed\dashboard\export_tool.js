// Generated by CoffeeScript 1.11.1
(function() {
  var ActionTool, ExportTool, ExportToolView, p,
    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },
    hasProp = {}.hasOwnProperty;

  p = require("core/properties");

  ActionTool = require("models/tools/actions/action_tool");

  ExportToolView = (function(superClass) {
    extend(ExportToolView, superClass);

    function ExportToolView() {
      return ExportToolView.__super__.constructor.apply(this, arguments);
    }

    ExportToolView.prototype.initialize = function(options) {
      ExportToolView.__super__.initialize.call(this, options);
      return this.listenTo(this.model, 'change:content', this["export"]);
    };

    ExportToolView.prototype["do"] = function() {
      return this.model.event = this.model.event + 1;
    };

    ExportToolView.prototype["export"] = function() {
      var a, blob, url;
      if (this.model.content != null) {
        blob = new Blob([this.model.content], {
          type: "text/plain"
        });
        url = window.URL.createObjectURL(blob);
        a = document.createElement("a");
        a.id = "bk-export-tool-link";
        a.style = "display: none";
        a.href = url;
        a.download = 'task-stream.html';
        document.body.appendChild(a);
        a.click();
        document.getElementById('bk-export-tool-link').remove();
        return window.URL.revokeObjectURL(url);
      }
    };

    return ExportToolView;

  })(ActionTool.View);

  ExportTool = (function(superClass) {
    extend(ExportTool, superClass);

    function ExportTool() {
      return ExportTool.__super__.constructor.apply(this, arguments);
    }

    ExportTool.prototype.default_view = ExportToolView;

    ExportTool.prototype.type = "ExportTool";

    ExportTool.prototype.tool_name = "Export";

    ExportTool.prototype.icon = "bk-tool-icon-save";

    ExportTool.define({
      event: [p.Int, 0],
      content: [p.String]
    });

    return ExportTool;

  })(ActionTool.Model);

  module.exports = {
    Model: ExportTool,
    View: ExportToolView
  };

}).call(this);
