
# This file was generated by 'versioneer.py' (0.29) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2025-07-14T14:59:10-0500",
 "dirty": false,
 "error": null,
 "full-revisionid": "0f0adefd2ad3dc69ace3aecf77a259c98feacbe5",
 "version": "2025.7.0"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
