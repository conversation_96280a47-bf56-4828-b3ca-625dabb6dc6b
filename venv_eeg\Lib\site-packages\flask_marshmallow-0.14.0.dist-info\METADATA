Metadata-Version: 2.1
Name: flask-marshmallow
Version: 0.14.0
Summary: Flask + marshmallow for beautiful APIs
Home-page: https://github.com/marshmallow-code/flask-marshmallow
Author: <PERSON>
Author-email: <EMAIL>
License: MIT
Project-URL: Issues, https://github.com/marshmallow-code/flask-marshmallow/issues
Project-URL: Funding, https://opencollective.com/marshmallow
Keywords: flask-marshmallow
Platform: UNKNOWN
Classifier: Development Status :: 4 - Beta
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Natural Language :: English
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Topic :: Internet :: WWW/HTTP :: Dynamic Content
Requires-Dist: Flask
Requires-Dist: marshmallow (>=2.0.0)
Requires-Dist: six (>=1.9.0)
Provides-Extra: dev
Requires-Dist: flask-sqlalchemy ; extra == 'dev'
Requires-Dist: pytest ; extra == 'dev'
Requires-Dist: mock ; extra == 'dev'
Requires-Dist: flake8 (==3.8.3) ; extra == 'dev'
Requires-Dist: pre-commit (~=2.4) ; extra == 'dev'
Requires-Dist: tox ; extra == 'dev'
Requires-Dist: marshmallow-sqlalchemy (<0.19.0,>=0.13.0) ; (python_version < "3.6") and extra == 'dev'
Requires-Dist: flake8-bugbear (==20.1.4) ; (python_version >= "3.5") and extra == 'dev'
Requires-Dist: marshmallow-sqlalchemy (>=0.13.0) ; (python_version >= "3.6") and extra == 'dev'
Provides-Extra: docs
Requires-Dist: marshmallow-sqlalchemy (>=0.13.0) ; extra == 'docs'
Requires-Dist: Sphinx (==3.2.1) ; extra == 'docs'
Requires-Dist: sphinx-issues (==1.2.0) ; extra == 'docs'
Provides-Extra: lint
Requires-Dist: flake8 (==3.8.3) ; extra == 'lint'
Requires-Dist: pre-commit (~=2.4) ; extra == 'lint'
Requires-Dist: flake8-bugbear (==20.1.4) ; (python_version >= "3.5") and extra == 'lint'
Provides-Extra: sqlalchemy
Requires-Dist: flask-sqlalchemy ; extra == 'sqlalchemy'
Requires-Dist: marshmallow-sqlalchemy (<0.19.0,>=0.13.0) ; (python_version < "3.6") and extra == 'sqlalchemy'
Requires-Dist: marshmallow-sqlalchemy (>=0.13.0) ; (python_version >= "3.6") and extra == 'sqlalchemy'
Provides-Extra: tests
Requires-Dist: flask-sqlalchemy ; extra == 'tests'
Requires-Dist: pytest ; extra == 'tests'
Requires-Dist: mock ; extra == 'tests'
Requires-Dist: marshmallow-sqlalchemy (<0.19.0,>=0.13.0) ; (python_version < "3.6") and extra == 'tests'
Requires-Dist: marshmallow-sqlalchemy (>=0.13.0) ; (python_version >= "3.6") and extra == 'tests'

*****************
Flask-Marshmallow
*****************

|pypi-package| |build-status| |docs| |marshmallow23| |black|

Flask + marshmallow for beautiful APIs
======================================

Flask-Marshmallow is a thin integration layer for `Flask`_ (a Python web framework) and `marshmallow`_ (an object serialization/deserialization library) that adds additional features to marshmallow, including URL and Hyperlinks fields for HATEOAS-ready APIs. It also (optionally) integrates with `Flask-SQLAlchemy <http://flask-sqlalchemy.pocoo.org/>`_.

Get it now
----------
::

    pip install flask-marshmallow


Create your app.

.. code-block:: python

    from flask import Flask
    from flask_marshmallow import Marshmallow

    app = Flask(__name__)
    ma = Marshmallow(app)

Write your models.

.. code-block:: python

    from your_orm import Model, Column, Integer, String, DateTime


    class User(Model):
        email = Column(String)
        password = Column(String)
        date_created = Column(DateTime, auto_now_add=True)


Define your output format with marshmallow.

.. code-block:: python


    class UserSchema(ma.Schema):
        class Meta:
            # Fields to expose
            fields = ("email", "date_created", "_links")

        # Smart hyperlinking
        _links = ma.Hyperlinks(
            {
                "self": ma.URLFor("user_detail", values=dict(id="<id>")),
                "collection": ma.URLFor("users"),
            }
        )


    user_schema = UserSchema()
    users_schema = UserSchema(many=True)


Output the data in your views.

.. code-block:: python

    @app.route("/api/users/")
    def users():
        all_users = User.all()
        return users_schema.dump(all_users)


    @app.route("/api/users/<id>")
    def user_detail(id):
        user = User.get(id)
        return user_schema.dump(user)


    # {
    #     "email": "<EMAIL>",
    #     "date_created": "Fri, 25 Apr 2014 06:02:56 -0000",
    #     "_links": {
    #         "self": "/api/users/42",
    #         "collection": "/api/users/"
    #     }
    # }


http://flask-marshmallow.readthedocs.io/
========================================

Learn More
==========

To learn more about marshmallow, check out its `docs <http://marshmallow.readthedocs.io/en/latest/>`_.



Project Links
=============

- Docs: https://flask-marshmallow.readthedocs.io/
- Changelog: http://flask-marshmallow.readthedocs.io/en/latest/changelog.html
- PyPI: https://pypi.python.org/pypi/flask-marshmallow
- Issues: https://github.com/marshmallow-code/flask-marshmallow/issues

License
=======

MIT licensed. See the bundled `LICENSE <https://github.com/marshmallow-code/flask-marshmallow/blob/master/LICENSE>`_ file for more details.


.. _Flask: http://flask.pocoo.org
.. _marshmallow: http://marshmallow.readthedocs.io

.. |pypi-package| image:: https://badgen.net/pypi/v/flask-marshmallow
    :target: https://pypi.org/project/flask-marshmallow/
    :alt: Latest version
.. |build-status| image:: https://dev.azure.com/sloria/sloria/_apis/build/status/marshmallow-code.flask-marshmallow?branchName=dev
    :target: https://dev.azure.com/sloria/sloria/_build/latest?definitionId=14&branchName=dev
    :alt: Build status
.. |docs| image:: https://readthedocs.org/projects/flask-marshmallow/badge/
   :target: https://flask-marshmallow.readthedocs.io/
   :alt: Documentation
.. |marshmallow23| image:: https://badgen.net/badge/marshmallow/2,3?list=1
    :target: https://marshmallow.readthedocs.io/en/latest/upgrading.html
    :alt: marshmallow 3 compatible
.. |black| image:: https://badgen.net/badge/code%20style/black/000
    :target: https://github.com/ambv/black
    :alt: code style: black


